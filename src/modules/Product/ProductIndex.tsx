import {View, Text, StyleSheet, ScrollView} from 'react-native';
import HeaderLogo from '../../Screen/Layout/headers/HeaderLogo';
import CategoryGrid from '../category/CategoryGrid';
import {RootScreen} from '../../router/router';
import {useNavigation} from '@react-navigation/native';
import HotProductsRow from './HotProductsRow';
import HotProductsSection from './HotProductsSection';
import FreeShipProductSection from './section/FreeShipProductSection';
import ProductBestSeller from './productBestSeller';
import SuggestionProductSection from './section/SuggestionProductSection';
import BannerSection from './section/bannerSection';
import MuchSearchSearch from './section/MuchSearchSearch';
import {ColorThemes} from '../../assets/skin/colors';

const ProductIndex = () => {
  const navigation = useNavigation<any>();

  const onSeeMore = (categoryId?: string) => {
    if (categoryId) {
      navigation.navigate(RootScreen.ProductListByCategory, {
        categoryId: categoryId,
      });
    } else {
      navigation.navigate(RootScreen.ProductListByCategory);
    }
  };
  return (
    <View
      style={{
        backgroundColor: ColorThemes.light.neutral_absolute_background_color,
      }}>
      <HeaderLogo />

      {/* banner */}
      <BannerSection />

      {/* danh mục */}
      <CategoryGrid
        numColumns={3}
        onCategoryPress={category => {
          onSeeMore(category.Id);
        }}
      />

      {/* sản phẩm hot */}
      <HotProductsSection
        title="Free ship"
        pageSize={10}
        onSeeAll={onSeeMore}
        onRefresh={() => {}}
      />

      {/* sản phẩm được tìm kiếm nhiều */}
      <MuchSearchSearch onSeeMore={onSeeMore} />

      {/* sản phẩm bán chạy */}
      <ProductBestSeller
        isSeeMore
        id="best-selling"
        onPressSeeMore={onSeeMore}
        onRefresh={() => {}}
      />

      {/* sản phẩm gợi ý */}
      <SuggestionProductSection onSeeAllPress={onSeeMore} />

      <View style={{height: 80}} />
    </View>
  );
};

const styles = StyleSheet.create({});

export default ProductIndex;
