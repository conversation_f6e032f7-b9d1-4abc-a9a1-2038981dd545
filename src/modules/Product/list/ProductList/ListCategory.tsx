import CategoryTabs from '../components/CategoryTabs';
import {useCallback, useRef} from 'react';
import {ScrollView, View} from 'react-native';
import {Category} from '../../../../redux/models/category';
import {useSelector} from 'react-redux';
import {RootState} from '../../../../redux/store/store';
import {useProductByCategoryHook} from '../../../../redux/reducers/ProductByCategoryReducer';

const ListCategory = () => {
  const productByCategoryHook = useProductByCategoryHook();
  const categoryScrollViewRef = useRef<ScrollView>(null);

  const {childrenCategory, currentCategory, parentCategory, filter} =
    useSelector((state: RootState) => state.productByCategory);

  const scrollToCategory = useCallback((catId: string, cats: Category[]) => {
    if (categoryScrollViewRef.current) {
      const categoryIndex = cats.findIndex(category => category.Id === catId);
      if (categoryIndex !== -1) {
        const scrollPosition = categoryIndex * 100;
        categoryScrollViewRef.current.scrollTo({
          x: scrollPosition,
          animated: true,
        });
      }
    }
  }, []);

  // // Handle category selection
  const handleCategorySelect = useCallback(
    (newCategoryId: string) => {
      try {
        if (!newCategoryId || !Array.isArray(childrenCategory)) {
          console.warn('Invalid category selection:', newCategoryId);
          return;
        }

        scrollToCategory(newCategoryId, childrenCategory);
        let categoryId = null;

        if (newCategoryId === 'all') {
          productByCategoryHook.setData('currentCategory', parentCategory);
          categoryId = parentCategory?.Id || null;
        } else {
          const selectedCategory = childrenCategory.find(
            category => category?.Id === newCategoryId,
          );
          productByCategoryHook.setData(
            'currentCategory',
            selectedCategory || null,
          );
          categoryId = newCategoryId;
        }

        console.log('categoryId', filter);
        productByCategoryHook.setData('filter', {
          ...filter,
          categoryId: categoryId || null,
        });
      } catch (error) {
        console.error('Error in handleCategorySelect:', error);
      }
    },
    [
      childrenCategory,
      scrollToCategory,
      filter,
      parentCategory,
      productByCategoryHook,
    ],
  );

  const getSelectedCategory = useCallback(() => {
    if (currentCategory?.Id === parentCategory?.Id) return 'all';
    return currentCategory?.Id || '';
  }, [currentCategory, parentCategory]);

  if (!Array.isArray(childrenCategory) || childrenCategory.length === 0) {
    return <View />;
  }

  return (
    <View style={{paddingBottom: 8}}>
      <CategoryTabs
        categories={childrenCategory}
        selectedCategory={getSelectedCategory()}
        onSelectCategory={handleCategorySelect}
        scrollViewRef={categoryScrollViewRef}
      />
    </View>
  );
};

export default ListCategory;
