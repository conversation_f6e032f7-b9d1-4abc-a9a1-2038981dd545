import CategoryTabs from '../components/CategoryTabs';
import {useCallback, useRef} from 'react';
import {ScrollView, View} from 'react-native';
import {Category} from '../../../../redux/models/category';
import {useSelector} from 'react-redux';
import {RootState} from '../../../../redux/store/store';
import {useProductByCategoryHook} from '../../../../redux/reducers/ProductByCategoryReducer';

const ListCategory = () => {
  const productByCategoryHook = useProductByCategoryHook();
  const categoryScrollViewRef = useRef<ScrollView>(null);
  const lastClickTime = useRef(0);
  const isUpdating = useRef(false);
  const DEBOUNCE_DELAY = 300; // 300ms debounce

  const {childrenCategory, currentCategory, parentCategory, filter} =
    useSelector((state: RootState) => state.productByCategory);

  const scrollToCategory = useCallback((catId: string, cats: Category[]) => {
    if (categoryScrollViewRef.current) {
      const categoryIndex = cats.findIndex(category => category.Id === catId);
      if (categoryIndex !== -1) {
        const scrollPosition = categoryIndex * 100;
        categoryScrollViewRef.current.scrollTo({
          x: scrollPosition,
          animated: true,
        });
      }
    }
  }, []);

  // // Handle category selection
  const handleCategorySelect = useCallback(
    (newCategoryId: string) => {
      try {
        const now = Date.now();

        // Prevent concurrent updates
        if (isUpdating.current) {
          console.log('Category update already in progress, skipping');
          return;
        }

        // Debounce rapid clicks
        if (now - lastClickTime.current < DEBOUNCE_DELAY) {
          console.log('Debouncing rapid category click');
          return;
        }
        lastClickTime.current = now;

        if (!newCategoryId || !Array.isArray(childrenCategory)) {
          console.warn('Invalid category selection:', newCategoryId);
          return;
        }

        isUpdating.current = true;

        scrollToCategory(newCategoryId, childrenCategory);
        let categoryId = null;

        if (newCategoryId === 'all') {
          productByCategoryHook.setData('currentCategory', parentCategory);
          categoryId = parentCategory?.Id || null;
        } else {
          const selectedCategory = childrenCategory.find(
            category => category?.Id === newCategoryId,
          );
          productByCategoryHook.setData(
            'currentCategory',
            selectedCategory || null,
          );
          categoryId = newCategoryId;
        }

        console.log('categoryId', categoryId);
        // Update filter without spreading the entire filter object to avoid dependency issues
        productByCategoryHook.setData('filter', {
          categoryId: categoryId || null,
          brandId: filter?.brandId || null,
          activeFilters: filter?.activeFilters || {},
          sortOption: filter?.sortOption || null,
          maxPrice: filter?.maxPrice || null,
          textSearch: filter?.textSearch || null,
          typeFilter: filter?.typeFilter || 'IsHot',
        });

        // Reset updating flag after a short delay
        setTimeout(() => {
          isUpdating.current = false;
        }, 100);
      } catch (error) {
        console.error('Error in handleCategorySelect:', error);
        isUpdating.current = false; // Reset flag on error
      }
    },
    [childrenCategory, scrollToCategory, parentCategory, productByCategoryHook], // Remove filter from dependencies to prevent infinite loops
  );

  const getSelectedCategory = useCallback(() => {
    if (currentCategory?.Id === parentCategory?.Id) return 'all';
    return currentCategory?.Id || '';
  }, [currentCategory, parentCategory]);

  if (!Array.isArray(childrenCategory) || childrenCategory.length === 0) {
    return <View />;
  }

  return (
    <View style={{paddingBottom: 8}}>
      <CategoryTabs
        categories={childrenCategory}
        selectedCategory={getSelectedCategory()}
        onSelectCategory={handleCategorySelect}
        scrollViewRef={categoryScrollViewRef}
      />
    </View>
  );
};

export default ListCategory;
