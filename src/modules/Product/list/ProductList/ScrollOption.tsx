import {useCallback, useEffect, useRef} from 'react';
import ScrollableTabs from '../../../news/scrollable/ScrollableTabs';
import {faFire, faTruckFast} from '@fortawesome/free-solid-svg-icons';
import {useSelector} from 'react-redux';
import {RootState} from '../../../../redux/store/store';
import {useProductByCategoryHook} from '../../../../redux/reducers/ProductByCategoryReducer';

const TABS_DATA = [
  {
    id: 'IsHot',
    label: 'HOT',
    icon: faFire,
  },
  {
    id: 'IsFreeShip',
    label: 'Freeship',
    icon: faTruckFast,
    color: '#3FB993',
  },
  // {
  //   id: 'new',
  //   label: 'Mới',
  //   icon: faBookOpen,
  // },
  // {
  //   id: 'favorite',
  //   label: 'Nhãn hàng ưa chuộng',
  //   icon: faHeart,
  // },
];

const ScrollOption = () => {
  const productByCategoryHook = useProductByCategoryHook();
  const {filter} = useSelector((state: RootState) => state.productByCategory);
  const isInitialized = useRef(false);
  const lastClickTime = useRef(0);
  const isUpdating = useRef(false);
  const DEBOUNCE_DELAY = 300; // 300ms debounce

  // Handle filter change - simplified to avoid infinite loops
  const handleFilterChange = useCallback(
    (filterId: any) => {
      try {
        const now = Date.now();

        // Prevent concurrent updates
        if (isUpdating.current) {
          console.log('Update already in progress, skipping');
          return;
        }

        // Debounce rapid clicks
        if (now - lastClickTime.current < DEBOUNCE_DELAY) {
          console.log('Debouncing rapid click');
          return;
        }
        lastClickTime.current = now;

        if (!filterId) {
          console.warn('Invalid filter change:', filterId);
          return;
        }

        isUpdating.current = true;

        // Create new active filters with only the selected filter
        const newActiveFilters = {
          [filterId]: true,
        };

        // Update filter with current values, avoiding dependency on filter object
        productByCategoryHook.setData('filter', {
          categoryId: filter?.categoryId || null,
          brandId: filter?.brandId || null,
          activeFilters: newActiveFilters,
          sortOption: filter?.sortOption || null,
          maxPrice: filter?.maxPrice || null,
          textSearch: filter?.textSearch || null,
          typeFilter: filter?.typeFilter || 'IsHot',
        });

        // Reset updating flag after a short delay
        setTimeout(() => {
          isUpdating.current = false;
        }, 100);
      } catch (error) {
        console.error('Error in handleFilterChange:', error);
        isUpdating.current = false; // Reset flag on error
      }
    },
    [productByCategoryHook], // Only depend on the hook, not the filter
  );

  // Initialize with IsHot filter only once
  useEffect(() => {
    if (!isInitialized.current) {
      try {
        isInitialized.current = true;
        // Set initial filter
        productByCategoryHook.setData('filter', {
          categoryId: null,
          brandId: null,
          activeFilters: {IsHot: true},
          sortOption: null,
          maxPrice: null,
          textSearch: null,
          typeFilter: 'IsHot',
        });
      } catch (error) {
        console.error('Error in ScrollOption useEffect:', error);
      }
    }
  }, []); // Empty dependency array - only run once on mount

  return (
    <ScrollableTabs onChangeTab={handleFilterChange as any} data={TABS_DATA} />
  );
};

export default ScrollOption;
