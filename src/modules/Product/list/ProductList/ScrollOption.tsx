import {useCallback, useEffect, useRef} from 'react';
import ScrollableTabs from '../../../news/scrollable/ScrollableTabs';
import {faFire, faTruckFast} from '@fortawesome/free-solid-svg-icons';
import {useSelector} from 'react-redux';
import {RootState} from '../../../../redux/store/store';
import {useProductByCategoryHook} from '../../../../redux/reducers/ProductByCategoryReducer';

const TABS_DATA = [
  {
    id: 'IsHot',
    label: 'HOT',
    icon: faFire,
  },
  {
    id: 'IsFreeShip',
    label: 'Freeship',
    icon: faTruckFast,
    color: '#3FB993',
  },
  // {
  //   id: 'new',
  //   label: 'Mới',
  //   icon: faBookOpen,
  // },
  // {
  //   id: 'favorite',
  //   label: 'Nhãn hàng ưa chuộng',
  //   icon: faHeart,
  // },
];

const ScrollOption = () => {
  const productByCategoryHook = useProductByCategoryHook();
  const {filter} = useSelector((state: RootState) => state.productByCategory);
  const isInitialized = useRef(false);
  const lastClickTime = useRef(0);
  const isUpdating = useRef(false);
  const lastFilterState = useRef<string | null>(null);
  const DEBOUNCE_DELAY = 300; // 300ms debounce

  // Validate filter state before update
  const validateFilterState = (newFilter: any) => {
    try {
      if (!newFilter || typeof newFilter !== 'object') {
        console.warn('Invalid filter object');
        return false;
      }
      return true;
    } catch (error) {
      console.error('Error validating filter state:', error);
      return false;
    }
  };

  // Handle filter change - simplified to avoid infinite loops
  const handleFilterChange = useCallback(
    async (filterId: any) => {
      try {
        const now = Date.now();

        // Prevent concurrent updates
        if (isUpdating.current) {
          console.log('Update already in progress, skipping');
          return;
        }

        // Debounce rapid clicks
        if (now - lastClickTime.current < DEBOUNCE_DELAY) {
          console.log('Debouncing rapid click');
          return;
        }
        lastClickTime.current = now;

        if (!filterId) {
          console.warn('Invalid filter change:', filterId);
          return;
        }

        isUpdating.current = true;

        // Check for state conflicts
        const currentFilterString = JSON.stringify(filter);
        if (
          lastFilterState.current &&
          lastFilterState.current !== currentFilterString
        ) {
          console.log(
            'ScrollOption detected state change, proceeding carefully',
          );
          // Add a small delay to let other component finish its update
          await new Promise(resolve => setTimeout(resolve, 50));
        }

        // Get current activeFilters and update only the selected one
        const currentActiveFilters = filter?.activeFilters || {};

        // Reset all filters first, then set the selected one
        const newActiveFilters = {
          IsHot: false,
          IsFreeShip: false,
          [filterId]: true,
        };

        console.log('ScrollOption updating filter:', {
          filterId,
          currentActiveFilters,
          newActiveFilters,
          categoryId: filter?.categoryId,
        });

        // Store current state for conflict detection
        lastFilterState.current = currentFilterString;

        // Prepare new filter state
        const newFilterState = {
          categoryId: filter?.categoryId || null,
          brandId: filter?.brandId || null,
          activeFilters: newActiveFilters,
          sortOption: filter?.sortOption || null,
          maxPrice: filter?.maxPrice || null,
          textSearch: filter?.textSearch || null,
          typeFilter: filter?.typeFilter || 'IsHot',
        };

        // Validate before updating
        if (!validateFilterState(newFilterState)) {
          console.error('ScrollOption: Invalid filter state, aborting update');
          isUpdating.current = false;
          return;
        }

        // Update filter with current values, avoiding dependency on filter object
        productByCategoryHook.setData('filter', newFilterState);

        // Reset updating flag after a short delay
        setTimeout(() => {
          isUpdating.current = false;
        }, 100);
      } catch (error) {
        console.error('Error in handleFilterChange:', error);
        isUpdating.current = false; // Reset flag on error
      }
    },
    [productByCategoryHook], // Only depend on the hook, not the filter
  );

  // Initialize with IsHot filter only once
  useEffect(() => {
    if (!isInitialized.current) {
      try {
        isInitialized.current = true;
        // Set initial filter
        productByCategoryHook.setData('filter', {
          categoryId: null,
          brandId: null,
          activeFilters: {IsHot: true},
          sortOption: null,
          maxPrice: null,
          textSearch: null,
          typeFilter: 'IsHot',
        });
      } catch (error) {
        console.error('Error in ScrollOption useEffect:', error);
      }
    }
  }, []); // Empty dependency array - only run once on mount

  return (
    <ScrollableTabs onChangeTab={handleFilterChange as any} data={TABS_DATA} />
  );
};

export default ScrollOption;
