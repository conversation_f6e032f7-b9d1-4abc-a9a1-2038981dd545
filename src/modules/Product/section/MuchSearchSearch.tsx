import React, {useState, useCallback, useEffect} from 'react';
import {
  SafeAreaView,
  View,
  Text,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  StatusBar,
} from 'react-native';
import ProductItem from '../FavoriteProductComponent/ProductItem';
import {FavoriteProduct} from '../../../redux/models/favoriteProduct';
import {productAction} from '../../../redux/actions/productAction';
import {Product} from '../../../redux/models/product';

// Header Component
interface HeaderProps {
  title: string;
  onSeeMore?: () => void;
}

const Header: React.FC<HeaderProps> = ({title, onSeeMore}) => (
  <View style={styles.header}>
    <Text style={styles.headerTitle}>{title}</Text>
    <TouchableOpacity onPress={onSeeMore}>
      <Text style={styles.seeMore}>Xem thêm</Text>
    </TouchableOpacity>
  </View>
);

// List Separator Component
const ItemSeparator: React.FC = () => <View style={styles.separator} />;

// Main Screen Component
const MostSearchedScreen: React.FC<{onSeeMore: () => void}> = ({onSeeMore}) => {
  const [products, setProducts] = useState<Product[]>([]);

  useEffect(() => {
    getData();
  }, []);

  const getData = async () => {
    const data = await productAction.find({
      page: 1,
      size: 5,
    });
    setProducts(data);
  };

  const handleSeeMore = useCallback(() => {
    onSeeMore();
  }, [onSeeMore]);

  const renderItem = useCallback(
    ({item}: {item: Product}) => <ProductItem item={item} />,
    [],
  );

  const keyExtractor = useCallback((item: Product) => item.Id, []);

  return (
    <SafeAreaView style={styles.screen}>
      <StatusBar barStyle="dark-content" />
      <Header title="Được tìm kiếm nhiều" onSeeMore={handleSeeMore} />
      {products?.length !== 0 &&
        products.map((item: any) => <ProductItem key={item.Id} item={item} />)}
    </SafeAreaView>
  );
};

// Styles
const styles = StyleSheet.create({
  screen: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  seeMore: {
    fontSize: 14,
    color: '#007AFF',
    fontWeight: '500',
  },
  separator: {
    height: 1,
    backgroundColor: '#EAEAEA',
    marginLeft: 96,
  },
});

export default MostSearchedScreen;
